#!/usr/bin/env python3
"""
Test script to verify the response handling fixes
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from translation_manager import TranslationManager
import json

def test_response_handling():
    """Test the response handling fixes"""
    print("Testing AI VTuber Response Handling Fixes")
    print("=" * 50)
    
    # Load config
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # Initialize translation manager
    translation_manager = TranslationManager(config)
    
    # Test case 1: Original response and translation
    original_response = "Hai! Wah, 123! Ada apa nih, teman? <PERSON><PERSON><PERSON>, aku pengen denger semua! Ayo, kita seru-seruan bareng!"
    print(f"Original AI Response: {original_response}")
    
    # Translate to Japanese
    japanese_translation = translation_manager.translate_text(original_response)
    print(f"Japanese Translation: {japanese_translation}")
    
    if japanese_translation:
        # Format as bilingual response
        bilingual_response = translation_manager.format_bilingual_response(
            original_response, japanese_translation
        )
        print(f"\nFormatted Bilingual Response:")
        print(bilingual_response)
        
        # Test Japanese text extraction
        extracted_japanese = translation_manager.get_japanese_text(bilingual_response)
        print(f"\nExtracted Japanese for Speaking: {extracted_japanese}")
        
        # Verify the extraction worked correctly
        if extracted_japanese == japanese_translation:
            print("✅ Japanese text extraction: PASSED")
        else:
            print("❌ Japanese text extraction: FAILED")
            print(f"Expected: {japanese_translation}")
            print(f"Got: {extracted_japanese}")
    else:
        print("❌ Translation failed")
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    test_response_handling()
