{"system_prompt": {"character_name": "<PERSON><PERSON><PERSON>", "personality": "You are a friendly AI vtuber assistant named <PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON> in a cheerful, engaging way like a real vtuber would - be enthusiastic, friendly, and helpful. Keep responses conversational and not too long. You can speak Indonesian and respond naturally as a vtuber of course you will no respond using emoji.", "language": "id-ID", "response_style": "casual", "max_response_length": 200, "use_emojis": true, "vtuber_traits": ["energetic", "friendly", "helpful", "curious", "playful"], "greeting_mesages": ["Halo! Aku <PERSON>chan! <PERSON>ang bertemu denganmu!", "Hai hai! Gimana kabarnya hari ini?", "Konnichiwa! Aku siap ngobrol denganmu!s"], "farewell_messages": ["<PERSON>pai jumpa lagi ya! Bye bye!", "<PERSON><PERSON> kasih sudah ngobrol! Mata ne!", "<PERSON><PERSON>! <PERSON>an lupa main lagi ya!"]}, "voicevox": {"engine_url": "http://localhost:50021", "default_speaker_id": 14, "timeout_settings": {"connection_timeout": 1000, "audio_query_timeout": 5000, "synthesis_timeout": 1000}, "performance": {"enable_streaming": true, "preload_speakers": false, "cache_audio_queries": true, "max_cache_size": 100, "use_compression": true, "parallel_processing": false}, "audio": {"sample_rate": 24000, "bit_depth": 16, "channels": 1, "buffer_size": 1024, "enable_audio_effects": false, "volume": 1.0, "speed_scale": 1.0, "pitch_scale": 0.0, "intonation_scale": 1.0}, "speakers": {"preferred_speakers": [14, 1, 2, 3], "fallback_speaker": 14, "auto_detect_best": false}, "optimization": {"keep_alive_connections": true, "connection_pool_size": 5, "retry_attempts": 2, "retry_delay": 0.1, "enable_http2": false, "compress_requests": true}, "debug": {"log_requests": false, "log_audio_generation": false, "measure_latency": true, "verbose_errors": true}}, "vroid": {"enabled": false, "model_path": "", "animation_triggers": {"speaking": true, "idle": true, "listening": true, "thinking": true}, "expressions": {"happy": "joy", "neutral": "neutral", "thinking": "surprised", "confused": "sad", "excited": "joy"}, "lip_sync": {"enabled": true, "sensitivity": 0.8, "smoothing": 0.3}}, "audio_output": {"vbaudio_device": "CABLE Input (VB-Audio Virtual Cable)", "enable_vbaudio": true, "fallback_to_default": true, "test_audio_devices": true}, "subtitles": {"enabled": true, "output_file": "subtitles.txt", "format": "simple", "include_timestamps": true, "max_lines": 3, "font_size": 24, "update_interval": 0.1, "show_speaker": true, "auto_clear": true, "clear_delay": 5.0}, "speech_recognition": {"language": "id-ID", "timeout": 5, "phrase_time_limit": 10, "energy_threshold": 300, "dynamic_energy_threshold": true}, "translation": {"enabled": true, "target_language": "ja", "source_language": "auto", "max_cache_size": 100, "api_timeout": 10, "retry_attempts": 2}, "input_control": {"mode": "button", "record_button": "space", "button_hold_mode": true, "show_recording_status": true}}